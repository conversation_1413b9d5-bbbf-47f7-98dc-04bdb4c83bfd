"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalFavorite = exports.Favorite = exports.Category = exports.Image = exports.Product = void 0;
// Export all models
var Product_1 = require("./Product");
Object.defineProperty(exports, "Product", { enumerable: true, get: function () { return Product_1.Product; } });
var Image_1 = require("./Image");
Object.defineProperty(exports, "Image", { enumerable: true, get: function () { return Image_1.Image; } });
var Category_1 = require("./Category");
Object.defineProperty(exports, "Category", { enumerable: true, get: function () { return Category_1.Category; } });
var Favorite_1 = require("./Favorite");
Object.defineProperty(exports, "Favorite", { enumerable: true, get: function () { return Favorite_1.Favorite; } });
var GlobalFavorite_1 = require("./GlobalFavorite");
Object.defineProperty(exports, "GlobalFavorite", { enumerable: true, get: function () { return GlobalFavorite_1.GlobalFavorite; } });
//# sourceMappingURL=index.js.map