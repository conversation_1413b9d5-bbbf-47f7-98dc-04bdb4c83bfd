const mongoose = require('mongoose');
const Product = require('./dist/models/Product').default;
const { Image } = require('./dist/models');

async function checkImageRecords() {
  try {
    await mongoose.connect('****************************************************************************************');
    console.log('数据库连接成功');

    // 查找阿里山鲜奶味西瓜子产品
    const product = await Product.findOne({
      'name.display': { $regex: '阿里山.*西瓜子', $options: 'i' }
    });

    if (!product) {
      console.log('未找到阿里山鲜奶味西瓜子产品');
      return;
    }

    console.log(`找到产品: ${product.name.display} (ID: ${product.productId})`);
    console.log('图片令牌:');

    const tokens = [];
    if (product.images) {
      Object.entries(product.images).forEach(([type, imageData]) => {
        if (imageData && typeof imageData === 'string' && !imageData.startsWith('http')) {
          console.log(`  - ${type}: ${imageData}`);
          tokens.push({ type, token: imageData });
        }
      });
    }

    console.log('\n检查Image表中的记录:');

    // 检查Image表中是否有对应记录
    for (const { type, token } of tokens) {
      console.log(`\n查找 ${type} 图片 (令牌: ${token}):`);

      // 按不同条件查找
      const queries = [
        { productId: product.productId, type: type, 'metadata.feishuFileToken': token },
        { productId: product.productId, type: type },
        { 'metadata.feishuFileToken': token },
        { productId: product.productId }
      ];

      for (let i = 0; i < queries.length; i++) {
        const images = await Image.find(queries[i]).limit(3);
        console.log(`  查询 ${i + 1}: 找到 ${images.length} 条记录`);

        if (images.length > 0) {
          images.forEach((img, idx) => {
            console.log(`    记录 ${idx + 1}:`);
            console.log(`      - imageId: ${img.imageId}`);
            console.log(`      - publicUrl: ${img.publicUrl}`);
            console.log(`      - feishuFileToken: ${img.metadata?.feishuFileToken || 'N/A'}`);
            console.log(`      - isActive: ${img.isActive}`);
          });
        }
      }
    }

    await mongoose.disconnect();
  } catch (error) {
    console.error('查询失败:', error.message);
    process.exit(1);
  }
}

checkImageRecords();
